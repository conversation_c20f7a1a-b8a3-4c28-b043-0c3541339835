import torch
import os

def check_gpu_status():
    """检查GPU状态和配置"""
    print("=== GPU状态检查 ===")
    
    # 检查CUDA是否可用
    print(f"CUDA是否可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        # GPU数量
        gpu_count = torch.cuda.device_count()
        print(f"GPU数量: {gpu_count}")
        
        # 当前GPU
        current_device = torch.cuda.current_device()
        print(f"当前GPU设备: {current_device}")
        
        # 每个GPU的详细信息
        for i in range(gpu_count):
            props = torch.cuda.get_device_properties(i)
            print(f"\nGPU {i}:")
            print(f"  名称: {props.name}")
            print(f"  总内存: {props.total_memory / 1024**3:.1f} GB")
            print(f"  多处理器数量: {props.multi_processor_count}")
            print(f"  CUDA计算能力: {props.major}.{props.minor}")
            
            # 内存使用情况
            if i == current_device:
                allocated = torch.cuda.memory_allocated(i) / 1024**3
                cached = torch.cuda.memory_reserved(i) / 1024**3
                print(f"  已分配内存: {allocated:.1f} GB")
                print(f"  缓存内存: {cached:.1f} GB")
        
        # 测试GPU计算
        print(f"\n=== GPU计算测试 ===")
        try:
            # 创建一个测试张量
            test_tensor = torch.randn(1000, 1000).cuda()
            result = torch.matmul(test_tensor, test_tensor)
            print("✓ GPU计算测试成功")
            
            # 清理内存
            del test_tensor, result
            torch.cuda.empty_cache()
            
        except Exception as e:
            print(f"✗ GPU计算测试失败: {e}")
    
    else:
        print("未检测到CUDA支持，将使用CPU")
    
    # 检查环境变量
    print(f"\n=== 环境变量 ===")
    cuda_visible = os.environ.get('CUDA_VISIBLE_DEVICES', '未设置')
    print(f"CUDA_VISIBLE_DEVICES: {cuda_visible}")
    
    # PyTorch版本信息
    print(f"\n=== PyTorch信息 ===")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA版本: {torch.version.cuda}")
    print(f"cuDNN版本: {torch.backends.cudnn.version()}")

def test_model_loading():
    """测试模型加载的GPU使用"""
    print(f"\n=== 模型加载GPU测试 ===")
    
    if not torch.cuda.is_available():
        print("GPU不可用，跳过模型加载测试")
        return
    
    try:
        # 测试HuggingFace模型的GPU使用
        from transformers import AutoTokenizer, AutoModel
        
        print("测试小型模型的GPU加载...")
        
        # 使用一个小型的中文模型进行测试
        model_name = "hfl/chinese-bert-wwm-ext"
        
        print(f"加载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        
        print(f"加载模型到GPU...")
        model = AutoModel.from_pretrained(
            model_name,
            torch_dtype=torch.float16,
            device_map="auto"
        )
        
        print(f"模型设备映射: {model.hf_device_map}")
        
        # 测试推理
        print("测试推理...")
        inputs = tokenizer("这是一个测试文本", return_tensors="pt")
        
        # 将输入移到GPU
        inputs = {k: v.cuda() for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = model(**inputs)
        
        print("✓ 模型GPU推理测试成功")
        print(f"输出形状: {outputs.last_hidden_state.shape}")
        print(f"输出设备: {outputs.last_hidden_state.device}")
        
        # 清理内存
        del model, tokenizer, inputs, outputs
        torch.cuda.empty_cache()
        
    except Exception as e:
        print(f"✗ 模型加载测试失败: {e}")

if __name__ == "__main__":
    check_gpu_status()
    test_model_loading()

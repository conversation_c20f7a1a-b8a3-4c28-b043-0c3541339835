import os
from langchain_community.document_loaders import PyPD<PERSON>oader, Docx2txtLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from langchain.chains import RetrievalQA
from langchain.prompts import PromptTemplate
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline

# ================== 1. 本地模型路径配置 ==================
QWEN3_MODEL_PATH = "./Qwen3-8B-Base"
QWEN3_EMBEDDING_PATH = "Qwen/Qwen3-Embedding-8B"

# ================== 2. 文档预处理 ==================
def load_and_split_documents(file_path):
    import os
    print(f"正在加载文档文件: {file_path}")
    print(f"文件是否存在: {os.path.exists(file_path)}")
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文档文件不存在: {file_path}")
    
    # 根据文件扩展名选择合适的加载器
    file_extension = os.path.splitext(file_path)[1].lower()
    
    if file_extension == '.pdf':
        loader = PyPDFLoader(file_path)
    elif file_extension in ['.doc', '.docx']:
        loader = Docx2txtLoader(file_path)
    else:
        raise ValueError(f"不支持的文件格式: {file_extension}")
    
    print(f"使用加载器: {type(loader).__name__}")
    documents = loader.load()
    print(f"加载的原始文档数量: {len(documents)}")
    
    if len(documents) > 0:
        for i, doc in enumerate(documents):
            content_length = len(doc.page_content.strip())
            print(f"文档 {i+1} 内容长度: {content_length}")
            if content_length > 0:
                print(f"文档 {i+1} 前200字符: {doc.page_content[:200]}")
            else:
                print(f"文档 {i+1} 内容为空")
    
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=512,
        chunk_overlap=50
    )
    split_docs = text_splitter.split_documents(documents)
    print(f"分割后的文档数量: {len(split_docs)}")
    return split_docs

# ================== 3. 向量化与构建 FAISS 数据库 ==================
def build_vectorstore(docs, embedding_model_path):
    from modelscope import snapshot_download
    import os
    
    # 检查本地是否已有模型
    local_model_path = f"./models/{embedding_model_path.replace('/', '_')}"
    if not os.path.exists(local_model_path):
        print(f"正在从ModelScope下载模型: {embedding_model_path}")
        local_model_path = snapshot_download(embedding_model_path, cache_dir="./models")
    else:
        print(f"使用本地模型: {local_model_path}")
    
    print(f"文档数量: {len(docs)}")
    if len(docs) == 0:
        raise ValueError("没有找到文档内容")
    
    # 打印前几个文档的内容以便调试
    for i, doc in enumerate(docs[:3]):
        print(f"文档 {i+1} 内容长度: {len(doc.page_content)}")
        print(f"文档 {i+1} 前100字符: {doc.page_content[:100]}")
    
    embedding_model = HuggingFaceEmbeddings(model_name=local_model_path)
    
    # 测试嵌入模型
    test_text = "这是一个测试文本"
    test_embedding = embedding_model.embed_query(test_text)
    print(f"测试嵌入向量维度: {len(test_embedding)}")
    
    vectorstore = FAISS.from_documents(docs, embedding_model)
    vectorstore.save_local("policy_vectorstore")
    return vectorstore

# ================== 主函数 ==================
def main():
    # 1. 加载并切分文档
    print("1. 加载并切分文档...")
    pdf_path = "./files/1.1.2交通运输部关于推动交通运输领域新型基础设施建设的指导意见.pdf"
    split_docs = load_and_split_documents(pdf_path)

    # 2. 构建向量数据库
    print("2. 构建向量数据库...")
    vectorstore = build_vectorstore(split_docs, QWEN3_EMBEDDING_PATH)

    print("RAG系统构建完成！")

if __name__ == "__main__":
    main()

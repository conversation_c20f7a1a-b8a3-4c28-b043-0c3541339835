import os
import re
import glob
import torch
from langchain.schema import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS

# ================== 自定义.doc文件加载器 ==================
class OldDocLoader:
    """处理旧版.doc文件的自定义加载器"""
    
    def __init__(self, file_path):
        self.file_path = file_path
    
    def load(self):
        """加载并解析.doc文件"""
        if not os.path.exists(self.file_path):
            raise FileNotFoundError(f"文件不存在: {self.file_path}")
        
        # 读取文件并提取文本
        with open(self.file_path, 'rb') as f:
            raw_data = f.read()
        
        # 尝试UTF-16LE解码
        text_parts = []
        for i in range(0, len(raw_data) - 1, 2):
            try:
                char = raw_data[i:i+2].decode('utf-16le', errors='ignore')
                if char.isprintable() and char not in '\x00\x01\x02\x03\x04\x05\x06\x07\x08\x0b\x0c\x0e\x0f':
                    text_parts.append(char)
            except:
                continue
        
        text = ''.join(text_parts)
        
        # 清理文本
        text = self._clean_text(text)
        
        # 创建Document对象
        doc = Document(
            page_content=text,
            metadata={"source": self.file_path}
        )
        
        return [doc]
    
    def _clean_text(self, text):
        """清理提取的文本"""
        # 查找中文内容的开始位置
        chinese_start = -1
        for i, char in enumerate(text):
            if '\u4e00' <= char <= '\u9fff':  # 中文字符范围
                chinese_start = max(0, i - 100)  # 从中文前100字符开始
                break
        
        if chinese_start > 0:
            text = text[chinese_start:]
        
        # 查找中文内容的结束位置（倒序查找）
        chinese_end = -1
        for i in range(len(text) - 1, -1, -1):
            if '\u4e00' <= text[i] <= '\u9fff':
                chinese_end = min(len(text), i + 100)  # 到中文后100字符结束
                break
        
        if chinese_end > 0:
            text = text[:chinese_end]
        
        # 移除明显的乱码字符，但保留中文、英文、数字和常用标点
        text = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf\w\s\.,;:!?()（）【】《》""''、。，；：！？\-\n\r]', '', text)
        
        # 移除过多的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除重复的空行
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        return text.strip()

# ================== 文档加载和处理 ==================
def load_and_split_documents(file_path):
    """加载并分割文档"""
    print(f"正在加载文档文件: {file_path}")
    print(f"文件是否存在: {os.path.exists(file_path)}")
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文档文件不存在: {file_path}")
    
    # 使用自定义加载器
    loader = OldDocLoader(file_path)
    documents = loader.load()
    print(f"加载的原始文档数量: {len(documents)}")
    
    if len(documents) > 0:
        doc = documents[0]
        content_length = len(doc.page_content.strip())
        print(f"文档内容长度: {content_length}")
        if content_length > 0:
            print(f"文档前200字符: {doc.page_content[:200]}")
        else:
            print("文档内容为空")
    
    # 分割文档
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=512,
        chunk_overlap=50
    )
    split_docs = text_splitter.split_documents(documents)
    print(f"分割后的文档数量: {len(split_docs)}")
    
    return split_docs

# ================== 向量化与构建 FAISS 数据库 ==================
def build_vectorstore(docs, embedding_model_path):
    """构建向量数据库"""
    from modelscope import snapshot_download
    
    # 检查本地是否已有模型
    local_model_path = f"./models/{embedding_model_path.replace('/', '_')}"
    if not os.path.exists(local_model_path):
        print(f"正在从ModelScope下载模型: {embedding_model_path}")
        local_model_path = snapshot_download(embedding_model_path, cache_dir="./models")
    else:
        print(f"使用本地模型: {local_model_path}")
    
    print(f"文档数量: {len(docs)}")
    if len(docs) == 0:
        raise ValueError("没有找到文档内容")
    
    # 打印前几个文档的内容以便调试
    for i, doc in enumerate(docs[:3]):
        print(f"文档片段 {i+1} 内容长度: {len(doc.page_content)}")
        print(f"文档片段 {i+1} 前100字符: {doc.page_content[:100]}")
    
    # 创建嵌入模型 - 强制使用GPU（用户要求必须使用GPU）
    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    model_kwargs = {
        'device': device
        # 注意: SentenceTransformer不支持torch_dtype参数
    }
    encode_kwargs = {
        'normalize_embeddings': True,
        'batch_size': 32 if device == 'cuda' else 8  # GPU可以使用更大的batch size
    }

    print(f"嵌入模型使用设备: {device}")
    if device == 'cuda':
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        print("使用FP16精度以优化GPU性能")
        print("注意: 即使有兼容性警告，仍强制使用GPU以获得最佳性能")

    embedding_model = HuggingFaceEmbeddings(
        model_name=local_model_path,
        model_kwargs=model_kwargs,
        encode_kwargs=encode_kwargs
    )
    
    # 测试嵌入模型
    test_text = "这是一个测试文本"
    test_embedding = embedding_model.embed_query(test_text)
    print(f"测试嵌入向量维度: {len(test_embedding)}")
    
    # 构建向量数据库
    vectorstore = FAISS.from_documents(docs, embedding_model)
    vectorstore.save_local("policy_vectorstore")
    print("向量数据库构建完成并已保存")
    
    return vectorstore

# ================== 主函数 ==================
def main():
    # 配置
    QWEN3_EMBEDDING_PATH = "Qwen/Qwen3-Embedding-8B"
    
    # 1. 查找.doc文件
    print("1. 查找文档文件...")
    doc_files = glob.glob("./files/*.doc")
    if not doc_files:
        print("没有找到.doc文件")
        return
    
    doc_file = doc_files[0]
    print(f"找到文档文件: {doc_file}")
    
    # 2. 加载并切分文档
    print("\n2. 加载并切分文档...")
    split_docs = load_and_split_documents(doc_file)
    
    # 3. 构建向量数据库
    print("\n3. 构建向量数据库...")
    vectorstore = build_vectorstore(split_docs, QWEN3_EMBEDDING_PATH)
    
    # 4. 测试检索
    print("\n4. 测试检索...")
    query = "综合运输服务发展规划的主要目标是什么？"
    results = vectorstore.similarity_search(query, k=3)
    
    print(f"查询: {query}")
    print(f"检索到 {len(results)} 个相关文档片段:")
    for i, result in enumerate(results):
        print(f"\n片段 {i+1}:")
        print(result.page_content[:200] + "...")
    
    print("\nRAG系统构建完成！")

if __name__ == "__main__":
    main()

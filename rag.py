import os
from langchain_community.document_loaders import PyPDFLoader, Docx2txtLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from langchain.chains import RetrievalQA
from langchain.prompts import PromptTemplate
# Note: QwenReranker is not available in current LangChain versions
# from langchain.rerankers import <PERSON>wenR<PERSON>ker
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
import torch

# ================== GPU检查函数 ==================
def check_gpu_availability():
    """检查GPU可用性"""
    print("=== GPU信息检查 ===")
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        print(f"✓ 检测到 {gpu_count} 个GPU设备")
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"  GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")

        current_device = torch.cuda.current_device()
        print(f"当前使用GPU: {current_device}")
        return True
    else:
        print("✗ 未检测到可用的GPU，将使用CPU")
        return False

# ================== 1. 本地模型路径配置 ==================
# 替换为本地 Qwen3-8B 的路径
QWEN3_MODEL_PATH = "./Qwen3-8B-Base"  # 确保已下载 Qwen3-8B 模型
QWEN3_EMBEDDING_PATH = "Qwen/Qwen3-Embedding-8B"  # 使用ModelScope的Qwen3嵌入模型
QWEN3_RERANKER_PATH = "Qwen/Qwen3-Reranker-8B"  # 可选：重排序模型

# ================== 2. 文档预处理 ==================
def load_and_split_documents(file_path):
    import os
    print(f"正在加载文档文件: {file_path}")
    print(f"文件是否存在: {os.path.exists(file_path)}")

    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文档文件不存在: {file_path}")

    # 根据文件扩展名选择合适的加载器
    file_extension = os.path.splitext(file_path)[1].lower()

    if file_extension == '.pdf':
        loader = PyPDFLoader(file_path)
    elif file_extension in ['.doc', '.docx']:
        loader = Docx2txtLoader(file_path)
    else:
        raise ValueError(f"不支持的文件格式: {file_extension}")

    print(f"使用加载器: {type(loader).__name__}")
    documents = loader.load()
    print(f"加载的原始文档数量: {len(documents)}")

    if len(documents) > 0:
        for i, doc in enumerate(documents):
            content_length = len(doc.page_content.strip())
            print(f"文档 {i+1} 内容长度: {content_length}")
            if content_length > 0:
                print(f"文档 {i+1} 前200字符: {doc.page_content[:200]}")
            else:
                print(f"文档 {i+1} 内容为空")

    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=512,  # 按条款级切分
        chunk_overlap=50
    )
    split_docs = text_splitter.split_documents(documents)
    print(f"分割后的文档数量: {len(split_docs)}")
    return split_docs

# ================== 3. 向量化与构建 FAISS 数据库 ==================
def build_vectorstore(docs, embedding_model_path):
    # 使用ModelScope下载模型
    from modelscope import snapshot_download
    import os

    # 检查本地是否已有模型
    local_model_path = f"./models/{embedding_model_path.replace('/', '_')}"
    if not os.path.exists(local_model_path):
        print(f"正在从ModelScope下载模型: {embedding_model_path}")
        local_model_path = snapshot_download(embedding_model_path, cache_dir="./models")
    else:
        print(f"使用本地模型: {local_model_path}")

    print(f"文档数量: {len(docs)}")
    if len(docs) == 0:
        raise ValueError("没有找到文档内容")

    # 打印前几个文档的内容以便调试
    for i, doc in enumerate(docs[:3]):
        print(f"文档 {i+1} 内容长度: {len(doc.page_content)}")
        print(f"文档 {i+1} 前100字符: {doc.page_content[:100]}")

    # 配置嵌入模型使用GPU
    model_kwargs = {'device': 'cuda' if torch.cuda.is_available() else 'cpu'}
    encode_kwargs = {'normalize_embeddings': True}

    embedding_model = HuggingFaceEmbeddings(
        model_name=local_model_path,
        model_kwargs=model_kwargs,
        encode_kwargs=encode_kwargs
    )

    print(f"嵌入模型使用设备: {model_kwargs['device']}")

    # 测试嵌入模型
    test_text = "这是一个测试文本"
    test_embedding = embedding_model.embed_query(test_text)
    print(f"测试嵌入向量维度: {len(test_embedding)}")

    vectorstore = FAISS.from_documents(docs, embedding_model)
    vectorstore.save_local("policy_vectorstore")  # 保存向量数据库
    return vectorstore

# ================== 4. 加载本地 Qwen3-8B 模型 ==================
def load_qwen3_model(model_path):
    print(f"正在加载Qwen3-8B模型: {model_path}")

    # 检查GPU内存
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"GPU内存: {gpu_memory:.1f} GB")

        # 根据GPU内存选择合适的数据类型
        if gpu_memory >= 24:
            torch_dtype = torch.float16
            print("使用FP16精度")
        elif gpu_memory >= 12:
            torch_dtype = torch.float16
            print("使用FP16精度")
        else:
            torch_dtype = torch.int8
            print("使用INT8精度以节省内存")
    else:
        torch_dtype = torch.float32
        print("使用CPU，采用FP32精度")

    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        device_map="auto",  # 自动分配显存
        trust_remote_code=True,
        torch_dtype=torch_dtype,
        low_cpu_mem_usage=True  # 减少CPU内存使用
    ).eval()

    print(f"模型加载完成，设备映射: {model.hf_device_map}")
    return tokenizer, model

# ================== 5. 定义提示词模板 ==================
def create_prompt_template():
    prompt_template = """
    你是一个政策合规专家，请严格依据以下检索到的政策条款回答问题：
    {context}

    问题：{question}

    回答要求：
    1. 必须引用具体政策名称和条款编号（如《XX办法》第X条）
    2. 使用集团公文格式（总则→细则→附则）
    """
    return PromptTemplate.from_template(prompt_template)

# ================== 6. 构建 RAG 链 ==================
def build_rag_chain(vectorstore, tokenizer, model, prompt_template):
    # 加载本地模型作为 LLM
    llm_pipeline = pipeline(
        "text-generation",
        model=model,
        tokenizer=tokenizer,
        max_new_tokens=200,
        temperature=0.3
    )
    
    # 构建检索器
    retriever = vectorstore.as_retriever(search_kwargs={"k": 5})  # 返回Top-5结果
    
    # 构建 RAG 链
    chain = RetrievalQA.from_chain_type(
        llm=llm_pipeline,
        chain_type="stuff",
        retriever=retriever,
        chain_type_kwargs={"prompt": prompt_template}
    )
    return chain

# ================== 7. 主函数 ==================
def main():
    # 0. 检查GPU可用性
    gpu_available = check_gpu_availability()
    print()

    # 1. 加载并切分文档
    print("1. 加载并切分文档...")
    pdf_path = "./files/10.1.5交通运输部关于印发《综合运输服务“十四五”发展规划》的通知.doc"  # 替换为实际政策文件路径
    split_docs = load_and_split_documents(pdf_path)

    # 2. 构建向量数据库
    print("2. 构建向量数据库...")
    vectorstore = build_vectorstore(split_docs, QWEN3_EMBEDDING_PATH)

    # 3. 加载本地 Qwen3-8B 模型
    print("3. 加载本地 Qwen3-8B 模型...")
    tokenizer, model = load_qwen3_model(QWEN3_MODEL_PATH)

    # 4. 定义提示词模板
    print("4. 定义提示词模板...")
    prompt_template = create_prompt_template()

    # 5. 构建 RAG 链
    print("5. 构建 RAG 链...")
    rag_chain = build_rag_chain(vectorstore, tokenizer, model, prompt_template)

    # 6. 测试查询
    print("6. 测试查询...")
    query = "综合运输服务“十四五”发展规划的指导思想是什么"
    response = rag_chain.run(query)
    print(f"问题: {query}")
    print(f"回答: {response}")

if __name__ == "__main__":
    main()
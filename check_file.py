import os

def check_file_format(file_path):
    """检查文件的实际格式"""
    print(f"检查文件: {file_path}")
    print(f"文件是否存在: {os.path.exists(file_path)}")
    
    if not os.path.exists(file_path):
        print("文件不存在")
        return
    
    # 读取文件头部字节来判断格式
    with open(file_path, 'rb') as f:
        header = f.read(20)
        print(f"文件头部字节: {header}")
        print(f"文件头部十六进制: {header.hex()}")
        
        # 检查常见文件格式的魔数
        if header.startswith(b'%PDF'):
            print("这是一个PDF文件")
        elif header.startswith(b'PK'):
            print("这是一个ZIP格式文件（可能是.docx）")
        elif header.startswith(b'\xd0\xcf\x11\xe0'):
            print("这是一个Microsoft Office旧格式文件（.doc/.xls/.ppt）")
        elif header.startswith(b'\x50\x4b\x03\x04'):
            print("这是一个ZIP文件（可能是新版Office文档）")
        else:
            print("未知文件格式")
    
    # 获取文件大小
    file_size = os.path.getsize(file_path)
    print(f"文件大小: {file_size} 字节")

if __name__ == "__main__":
    file_path = "./files/10.1.5交通运输部关于印发《综合运输服务\"十四五\"发展规划》的通知.doc"
    check_file_format(file_path)

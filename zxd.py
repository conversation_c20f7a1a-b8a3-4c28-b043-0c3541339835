
import os
import re
import glob
import torch
from langchain.schema import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from modelscope import snapshot_download

QWEN3_EMBEDDING_PATH = "Qwen/Qwen3-Embedding-8B"
TEXT = """
为科学统筹“十四五”期交通运输立法工作，切实增强立法的及时性、系统性、针对性、有效性，根据《中共中央关于全面推进依法治国若干重大问题的决定》《交通强国建设纲要》《国家综合立体交通网规划纲要》《交通运输部关于完善综合交通法规体系的意见》，制定本规划。
一、现状与形势
（一）发展基础。
一是构建综合交通法规体系框架。立足综合交通运输发展谋篇布局，构建了由跨运输方式法规系统、铁路法规系统、公路法规系统、水路法规系统、民航法规系统、邮政法规系统等六个系统构成的综合交通法规体系框架。截至“十三五”期末，形成了包括8部法律、43部行政法规、300部地方性法规、288件部门规章、290件地方政府规章的综合交通法规体系，使交通运输各领域、各门类基本做到有法可依。二是法律法规规章制修订工作取得积极进展。新修订的《中华人民共和国海上交通安全法》颁布实施，全面提升海上交通安全管理水平。《快递暂行条例》颁布实施，有效促进快递业提质升级。落实国务院关于“放管服”改革、“证照分离”改革要求以及党和国家机构改革部署要求，修改法律8件次、行政法规23件次，有效促进立法与改革决策相衔接。将8件行业发展急需的法律、行政法规草案送审稿报送国务院。出台了加强安全生产、服务群众出行、优化营商环境、防治污染等方面的部门规章234件。三是立法工作领导和运行机制不断完善。交通运输部党组加强对交通运输立法工作的领导。颁布了《交通运输法规制定程序规定》，全方位规范法规立项、起草、论证、协调、审议工作流程。健全开门立法、民主立法、专家参与工作机制，完善法律顾问和公职律师制度，充分听取吸收各有关方面意见。健全法规清理工作机制。
与此同时，交通运输立法工作还存在以下突出问题：一是综合交通运输法规尚存空白，部分重点立法项目尚未取得实质性突破，还需要争取理解、扩大共识。二是旺盛的立法需求与有限的立法资源的矛盾还没有得到有效解决，立法工作的计划性、统筹性、协调性有待进一步增强。三是立法工作的实效性有待进一步提高，需要进一步突出问题导向，精准回应行业发展需求。
（二）形势要求。
“十四五”时期是加快建设交通强国的第一个五年期，是推动交通运输高质量发展的重要时期，迫切需要更好发挥立法的引领和推动作用，在法治轨道上促进交通运输各方面制度更加成熟、更加定型，以高质量立法保障和促进交通运输高质量发展。
“十四五”时期，交通运输立法工作要紧扣中央重大战略决策部署，聚焦行业改革发展重点，确保重大立法项目取得突破性进展，助力加快建设交通强国、推动交通运输高质量发展。要立足新发展阶段，贯彻新发展理念，服务构建新发展格局，着力补短板、填空白、强弱项，持续降低制度性交易成本，推进交通运输对外开放合作，推动形成统一开放的交通运输市场。要落实全面依法治国、建设交通运输法治政府部门要求，通过加强和改进立法工作，保障改革，促进发展，维护稳定，防范风险，实现交通运输行业长治久安。要更加注重扩大社会参与、回应社会关切、解决实际问题，不断增强人民群众的获得感、幸福感、安全感，建设人民满意交通。要提升立法质量，在法治轨道上促进制度建设和治理效能更好转化融合，实现交通运输行业良法善治。
"""

def main() :
    # 1. 准备文本
    # print(TEXT);

    # 2. 分割文本
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=512,
        chunk_overlap=50
    )
    # 先将字符串转换为Document对象
    documents = [Document(page_content=TEXT)]
    split_docs = text_splitter.split_documents(documents)
    print(f"分割后的文档数量: {len(split_docs)}")

    # 3. 构建向量数据库
    print("\n3. 构建向量数据库...")
    # 检查本地是否已有模型
    local_model_path = f"./models/{QWEN3_EMBEDDING_PATH.replace('/', '_')}"
    if not os.path.exists(local_model_path):
        print(f"正在从ModelScope下载模型: {QWEN3_EMBEDDING_PATH}")
        local_model_path = snapshot_download(QWEN3_EMBEDDING_PATH, cache_dir="./models")
    else:
        print(f"使用本地模型: {local_model_path}")
    
    print(f"文档数量: {len(split_docs)}")
    if len(split_docs) == 0:
        raise ValueError("没有找到文档内容")
    
    # 打印前几个文档的内容以便调试
    for i, doc in enumerate(split_docs[:3]):
        print(f"文档片段 {i+1} 内容长度: {len(doc.page_content)}")
        print(f"文档片段 {i+1} 前100字符: {doc.page_content[:100]}")
    
    # 创建嵌入模型 - 由于CUDA兼容性问题，临时使用CPU
    # device = 'cuda' if torch.cuda.is_available() else 'cpu'
    device = 'cpu'  # 临时使用CPU，直到PyTorch支持RTX 5090

    model_kwargs = {
        'device': device
        # 注意: SentenceTransformer不支持torch_dtype参数
    }
    encode_kwargs = {
        'normalize_embeddings': True,
        'batch_size': 32 if device == 'cuda' else 8  # GPU可以使用更大的batch size
    }

    print(f"嵌入模型使用设备: {device}")
    if device == 'cuda':
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        print("使用FP16精度以优化GPU性能")
        print("注意: 即使有兼容性警告，仍强制使用GPU以获得最佳性能")

    embedding_model = HuggingFaceEmbeddings(
        model_name=local_model_path,
        model_kwargs=model_kwargs,
        encode_kwargs=encode_kwargs
    )
    
    # 测试嵌入模型
    test_text = "这是一个测试文本"
    test_embedding = embedding_model.embed_query(test_text)
    print(f"测试嵌入向量维度: {len(test_embedding)}")
    
    # 构建向量数据库
    vectorstore = FAISS.from_documents(split_docs, embedding_model)
    vectorstore.save_local("policy_vectorstore")
    print("向量数据库构建完成并已保存")
    
    # 4. 测试检索
    print("\n4. 测试检索...")
    query = "十四五中的交通运输的发展基础是什么"
    results = vectorstore.similarity_search(query, k=3)
    
    print(f"查询: {query}")
    print(f"检索到 {len(results)} 个相关文档片段:")
    for i, result in enumerate(results):
        print(f"\n片段 {i+1}:")
        print(result.page_content[:200] + "...")
    
    print("\nRAG系统构建完成！")



if __name__ == "__main__":
    main()
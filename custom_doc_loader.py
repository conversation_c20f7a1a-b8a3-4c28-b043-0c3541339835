import os
import re
from langchain.schema import Document

class OldDocLoader:
    """处理旧版.doc文件的自定义加载器"""
    
    def __init__(self, file_path):
        self.file_path = file_path
    
    def load(self):
        """加载并解析.doc文件"""
        if not os.path.exists(self.file_path):
            raise FileNotFoundError(f"文件不存在: {self.file_path}")
        
        # 读取文件并提取文本
        with open(self.file_path, 'rb') as f:
            raw_data = f.read()
        
        # 方法1: 尝试UTF-16LE解码
        text_parts = []
        for i in range(0, len(raw_data) - 1, 2):
            try:
                char = raw_data[i:i+2].decode('utf-16le', errors='ignore')
                if char.isprintable() and char not in '\x00\x01\x02\x03\x04\x05\x06\x07\x08\x0b\x0c\x0e\x0f':
                    text_parts.append(char)
            except:
                continue
        
        text = ''.join(text_parts)
        
        # 清理文本
        text = self._clean_text(text)
        
        # 创建Document对象
        doc = Document(
            page_content=text,
            metadata={"source": self.file_path}
        )
        
        return [doc]
    
    def _clean_text(self, text):
        """清理提取的文本"""
        # 移除过多的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除明显的乱码字符
        text = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf\w\s\.,;:!?()（）【】《》""''、。，；：！？]', '', text)
        
        # 移除过短的片段（可能是乱码）
        lines = text.split('\n')
        cleaned_lines = []
        for line in lines:
            line = line.strip()
            if len(line) > 3:  # 只保留长度大于3的行
                cleaned_lines.append(line)
        
        text = '\n'.join(cleaned_lines)
        
        # 移除重复的空行
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        return text.strip()

def test_custom_loader():
    """测试自定义加载器"""
    import glob
    
    doc_files = glob.glob("./files/*.doc")
    if not doc_files:
        print("没有找到.doc文件")
        return
    
    doc_file = doc_files[0]
    print(f"测试文件: {doc_file}")
    
    loader = OldDocLoader(doc_file)
    documents = loader.load()
    
    print(f"加载的文档数量: {len(documents)}")
    if documents:
        doc = documents[0]
        print(f"文档内容长度: {len(doc.page_content)}")
        print(f"前500字符:")
        print(doc.page_content[:500])
        print("\n" + "="*50)
        print(f"后500字符:")
        print(doc.page_content[-500:])

if __name__ == "__main__":
    test_custom_loader()

import os
import glob

def test_doc_reading():
    """测试读取.doc文件的不同方法"""
    
    # 找到.doc文件
    doc_files = glob.glob("./files/*.doc")
    if not doc_files:
        print("没有找到.doc文件")
        return
    
    doc_file = doc_files[0]
    print(f"测试文件: {doc_file}")
    
    # 方法1: 尝试使用docx2txt (可能不支持旧格式)
    print("\n=== 方法1: docx2txt ===")
    try:
        import docx2txt
        text = docx2txt.process(doc_file)
        print(f"成功读取，内容长度: {len(text)}")
        if text:
            print(f"前200字符: {text[:200]}")
        else:
            print("内容为空")
    except Exception as e:
        print(f"docx2txt失败: {e}")
    
    # 方法2: 尝试使用antiword
    print("\n=== 方法2: antiword ===")
    try:
        import antiword
        text = antiword.extract(doc_file)
        print(f"成功读取，内容长度: {len(text)}")
        if text:
            print(f"前200字符: {text[:200]}")
        else:
            print("内容为空")
    except Exception as e:
        print(f"antiword失败: {e}")
    
    # 方法3: 尝试使用python-docx (可能不支持旧格式)
    print("\n=== 方法3: python-docx ===")
    try:
        from docx import Document
        doc = Document(doc_file)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        print(f"成功读取，内容长度: {len(text)}")
        if text:
            print(f"前200字符: {text[:200]}")
        else:
            print("内容为空")
    except Exception as e:
        print(f"python-docx失败: {e}")
    
    # 方法4: 尝试直接读取为文本（可能乱码）
    print("\n=== 方法4: 直接读取 ===")
    try:
        with open(doc_file, 'rb') as f:
            raw_data = f.read()
            # 尝试提取可能的文本内容
            text_parts = []
            for i in range(0, len(raw_data) - 1, 2):
                try:
                    char = raw_data[i:i+2].decode('utf-16le', errors='ignore')
                    if char.isprintable() and char not in '\x00\x01\x02\x03\x04\x05\x06\x07\x08\x0b\x0c\x0e\x0f':
                        text_parts.append(char)
                except:
                    continue
            
            text = ''.join(text_parts)
            # 清理文本
            text = ' '.join(text.split())
            print(f"提取的文本长度: {len(text)}")
            if text:
                print(f"前200字符: {text[:200]}")
            else:
                print("没有提取到有效文本")
    except Exception as e:
        print(f"直接读取失败: {e}")

if __name__ == "__main__":
    test_doc_reading()
